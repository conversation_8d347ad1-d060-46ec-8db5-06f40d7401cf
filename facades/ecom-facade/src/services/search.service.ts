import {injectable, BindingScope, service} from '@loopback/core';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  Asset,
  AssetResponseDto,
  Collection,
  CollectionWithRelations,
  Facet,
  FacetValue,
  FilterGroup,
  FilterValue,
  Product,
  ProductFacetValue,
  ProductVariant,
  ProductVariantFacetValue,
  ProductVariantWithRelations,
} from '../models';
import {OptionUnit} from '@local/core';
import {AnyObject, Filter, repository, Where} from '@loopback/repository';
import {ProductVariantService} from './product-variant.service';
import {SearchFilterRepository} from '../repositories';
import {FILTER_CACHE_TTL, MINIMUM_DISCOUNT_THRESHOLD} from '../constants';
import {getDiscountLabel, getProductSearchFilter} from '../utils/search.util';

@injectable({scope: BindingScope.TRANSIENT})
export class SearchService {
  constructor(
    @restService(Product)
    private readonly productProxy: ModifiedRestService<Product>,
    @restService(ProductVariant)
    private readonly productVariantProxy: ModifiedRestService<ProductVariant>,
    @restService(ProductVariantFacetValue)
    private readonly productVariantFacetValueProxy: ModifiedRestService<ProductVariantFacetValue>,
    @restService(ProductFacetValue)
    private readonly productFacetValueProxy: ModifiedRestService<ProductFacetValue>,
    @restService(FacetValue)
    private readonly facetValueProxy: ModifiedRestService<FacetValue>,
    @restService(Facet)
    private readonly facetProxy: ModifiedRestService<Facet>,
    @restService(Collection)
    private readonly collectionProxy: ModifiedRestService<Collection>,
    @service(ProductVariantService)
    private readonly productVariantService: ProductVariantService,
    @repository(SearchFilterRepository)
    private readonly searchFilterRepository: SearchFilterRepository,
  ) {}

  private async prepareCondition(
    keyword: string,
  ): Promise<Where<ProductVariant>> {
    const cachedFilter = await this.searchFilterRepository.get(keyword);
    if (cachedFilter?.where) {
      return cachedFilter.where;
    }

    const facetValues = await this.facetValueProxy.find({
      where: {name: {ilike: `%${keyword}%`}},
      fields: {id: true, name: true, facetId: true},
      include: [
        {
          relation: 'facet',
          scope: {
            fields: {id: true, name: true},
          },
        },
      ],
    });

    const collections = await this.collectionProxy.find({
      where: {name: {ilike: `%${keyword}%`}},
      fields: {name: true, id: true},
      include: [
        {relation: 'childrens', scope: {fields: {name: true, id: true}}},
      ],
    });

    const productFacetValues = await this.productFacetValueProxy.find({
      where: {facetValueId: {inq: facetValues.map(item => item.id ?? '')}},
      fields: {id: true, facetValueId: true, productId: true},
    });

    const productVariantFacets = await this.productVariantFacetValueProxy.find({
      where: {facetValueId: {inq: facetValues.map(item => item.id ?? '')}},
      fields: {id: true, facetValueId: true, productVariantId: true},
    });
    const collectionIds = (collections as CollectionWithRelations[]).reduce(
      (acc: string[], collection) => {
        acc.push(collection.id ?? ''); // add parent ID
        if (collection.childrens && Array.isArray(collection.childrens)) {
          acc.push(...collection.childrens.map(child => child.id ?? '')); // add children IDs
        }
        if (collection.parentId) {
          acc.push(collection.parentId);
        }
        return acc;
      },
      [],
    );

    const products = await this.productProxy.find({
      where: {
        or: [
          {collectionId: {inq: collectionIds}},
          {name: {ilike: `%${keyword}%`}},
        ],
      },
    });

    const productIds = [
      ...products.map(p => p.id ?? ''),
      ...productFacetValues.map(pf => pf.productId ?? ''),
    ];
    const where: Where<ProductVariant> = {
      or: [],
    };

    if (keyword) {
      where.or.push({name: {ilike: `%${keyword}%`}});
    }

    if (productIds.length > 0) {
      where.or.push({productId: {inq: productIds}});
    }
    if (productVariantFacets.length) {
      where.or.push({
        id: {inq: productVariantFacets.map(pvf => pvf.productVariantId ?? '')},
      });
    }
    await this.searchFilterRepository.set(
      keyword,
      {keyword, where},
      {ttl: FILTER_CACHE_TTL},
    );
    return where;
  }

  private async prepareConditionFromFacetOrCollection({
    keyword,
    facetValueIds,
    collectionIds,
  }: {
    keyword?: string;
    facetValueIds?: string[];
    collectionIds?: string[];
  }): Promise<Where<ProductVariant>> {
    const andConditions: Where<ProductVariant>[] = [];

    if (keyword) {
      andConditions.push({name: {ilike: `%${keyword}%`}});
    }

    if (collectionIds?.length) {
      const products = await this.productProxy.find({
        where: {
          collectionId: {inq: collectionIds},
        },
        fields: {id: true},
      });
      const productIds = products.map(p => p.id ?? '');
      if (productIds.length) {
        andConditions.push({productId: {inq: productIds}});
      }
    }

    if (facetValueIds?.length) {
      const facetVariantMappings =
        await this.productVariantFacetValueProxy.find({
          where: {
            facetValueId: {inq: facetValueIds},
          },
          fields: {productVariantId: true},
        });
      const variantIds = facetVariantMappings.map(
        m => m.productVariantId ?? '',
      );
      if (variantIds.length) {
        andConditions.push({id: {inq: variantIds}});
      }
    }

    // Return appropriate where condition based on number of conditions
    if (andConditions.length === 0) {
      return {};
    } else if (andConditions.length === 1) {
      return andConditions[0];
    } else {
      return {and: andConditions};
    }
  }

  async searchSuggestions(keyword: string): Promise<Partial<ProductVariant[]>> {
    if (!keyword?.trim()) return [];

    const normalize = (text: string) =>
      text
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .trim();

    const getCoreWords = (name: string) =>
      normalize(name)
        .split(/[\s\(\)\-]+/)
        .filter(Boolean);

    const normalizedKeyword = normalize(keyword);

    const products = await this.productVariantProxy.find({
      fields: {id: true, name: true},
      include: [
        {
          relation: 'product',
          scope: {fields: {sellerId: true, status: true}},
        },
      ],
    });

    const variants = products as ProductVariantWithRelations[];

    const activeVariants =
      await this.productVariantService.filterOutInactiveSellerProductvariants(
        variants,
      );

    const levenshtein = (a: string, b: string): number => {
      const matrix: number[][] = [];
      for (let i = 0; i <= b.length; i++) matrix[i] = [i];
      for (let j = 0; j <= a.length; j++) matrix[0][j] = j;

      for (let i = 1; i <= b.length; i++) {
        for (let j = 1; j <= a.length; j++) {
          matrix[i][j] =
            b[i - 1] === a[j - 1]
              ? matrix[i - 1][j - 1]
              : Math.min(
                  matrix[i - 1][j - 1] + 1,
                  matrix[i][j - 1] + 1,
                  matrix[i - 1][j] + 1,
                );
        }
      }
      return matrix[b.length][a.length];
    };

    const results = activeVariants
      .map(variant => {
        const name = variant.name ?? '';
        const words = getCoreWords(name);

        let score = Infinity;

        for (const word of words) {
          if (word.startsWith(normalizedKeyword)) {
            score = 0; // Highest priority: prefix match
            break;
          } else {
            const dist = levenshtein(normalizedKeyword, word);
            if (dist < score) score = dist;
          }
        }

        return {variant, score};
      })
      .filter(match => match.score <= 2)
      .sort((a, b) => a.score - b.score)
      .map(match => match.variant);

    return results.slice(0, 10);
  }

  async getFilters(
    keyword?: string,
    facetValueIdsStr?: string,
    collectionIdsStr?: string,
    thresholdType?: 'price' | 'discount',
    thresholdValue?: number,
    thresholdOperator?: 'above' | 'below',
  ): Promise<FilterGroup[]> {
    const facetValueIds = facetValueIdsStr?.split(',').filter(Boolean);
    const collectionIds = collectionIdsStr?.split(',').filter(Boolean);

    let where: Where<ProductVariant>;
    const andConditions: Where<ProductVariant>[] = [];

    if ((facetValueIds?.length ?? 0) > 0 || (collectionIds?.length ?? 0) > 0) {
      where = await this.prepareConditionFromFacetOrCollection({
        keyword,
        facetValueIds,
        collectionIds,
      });
      andConditions.push(where);
    } else if (keyword) {
      where = await this.prepareCondition(keyword ?? '');
      andConditions.push(where);
    }

    // Add threshold filter if provided
    if (thresholdType && thresholdValue !== undefined && thresholdOperator) {
      const thresholdWhereIds = await this.prepareThresholdFilter(
        thresholdType,
        thresholdValue,
        thresholdOperator,
      );
      if (thresholdWhereIds.length) {
        andConditions.push({id: {inq: thresholdWhereIds}});
      }
    }

    // Combine conditions
    if (andConditions.length > 1) {
      where = {and: andConditions};
    } else if (andConditions.length === 1) {
      where = andConditions[0];
    } else {
      where = {};
    }

    const variants = (await this.productVariantProxy.find({
      ...getProductSearchFilter(),
      where,
    })) as ProductVariantWithRelations[];
    console.log('🚀 ~ SearchService ~ getFilters ~ variants:', variants);

    const mergedVariants = variants;

    if (keyword) {
      const fuzzyResults = (await this.searchSuggestions(
        keyword,
      )) as ProductVariant[];

      if (fuzzyResults.length) {
        const fuzzyIds = fuzzyResults.map(f => f.id ?? '');

        // Apply the same filters to fuzzy results as applied to main search
        const fuzzyConditions: Where<ProductVariant>[] = [];
        fuzzyConditions.push({id: {inq: fuzzyIds}});

        // Add facet filter if present
        if (facetValueIds?.length) {
          const facetVariantMappings =
            await this.productVariantFacetValueProxy.find({
              where: {facetValueId: {inq: facetValueIds}},
              fields: {productVariantId: true},
            });
          const variantIds = facetVariantMappings.map(
            m => m.productVariantId ?? '',
          );
          if (variantIds.length) {
            fuzzyConditions.push({id: {inq: variantIds}});
          }
        }

        // Add collection filter if present
        if (collectionIds?.length) {
          const products = await this.productProxy.find({
            where: {collectionId: {inq: collectionIds}},
            fields: {id: true},
          });
          const productIds = products.map(p => p.id ?? '');
          if (productIds.length) {
            fuzzyConditions.push({productId: {inq: productIds}});
          }
        }

        // Add threshold filter if present
        if (
          thresholdType &&
          thresholdValue !== undefined &&
          thresholdOperator
        ) {
          const thresholdWhereIds = await this.prepareThresholdFilter(
            thresholdType,
            thresholdValue,
            thresholdOperator,
          );
          if (thresholdWhereIds.length) {
            fuzzyConditions.push({id: {inq: thresholdWhereIds}});
          }
        }

        const fuzzyWhere =
          fuzzyConditions.length > 1
            ? {and: fuzzyConditions}
            : fuzzyConditions[0];

        const fuzzyFull = (await this.productVariantProxy.find({
          ...getProductSearchFilter(),
          where: fuzzyWhere,
        })) as ProductVariantWithRelations[];

        const seen = new Set(mergedVariants.map(v => v.id));
        for (const f of fuzzyFull) {
          if (f.id && !seen.has(f.id)) {
            mergedVariants.push(f);
            seen.add(f.id);
          }
        }
      }
    }

    return this.buildFilters(mergedVariants);
  }

  async search({
    keyword,
    facetValueIds,
    collectionIds,
    priceRange,
    filter,
    thresholdType,
    thresholdValue,
    thresholdOperator,
  }: {
    keyword?: string;
    facetValueIds?: string[];
    collectionIds?: string[];
    priceRange?: number[];
    filter?: Filter<ProductVariant>;
    thresholdType?: 'price' | 'discount';
    thresholdValue?: number;
    thresholdOperator?: 'above' | 'below';
  }): Promise<ProductVariant[]> {
    let where: Where<ProductVariant> = {...(filter?.where ?? {})};
    const andConditions: Where<ProductVariant>[] = [];

    // --- PRICE RANGE ---
    if (priceRange?.length === 2) {
      const priceWhereIds = await this.preparePriceFilter(priceRange);
      if (priceWhereIds.length) {
        andConditions.push({id: {inq: priceWhereIds}});
      }
    }

    // --- THRESHOLD FILTER (Price/Discount) ---
    if (thresholdType && thresholdValue !== undefined && thresholdOperator) {
      const thresholdWhereIds = await this.prepareThresholdFilter(
        thresholdType,
        thresholdValue,
        thresholdOperator,
      );
      if (thresholdWhereIds.length) {
        andConditions.push({id: {inq: thresholdWhereIds}});
      }
    }

    // --- KEYWORD, FACET, AND COLLECTION FILTERS ---
    if ((facetValueIds?.length ?? 0) > 0 || (collectionIds?.length ?? 0) > 0) {
      // Use the same logic as getFilters when facets or collections are present
      const combinedWhere = await this.prepareConditionFromFacetOrCollection({
        keyword,
        facetValueIds,
        collectionIds,
      });
      if (Object.keys(combinedWhere).length > 0) {
        andConditions.push(combinedWhere);
      }
    } else if (keyword) {
      // Use the original keyword logic only when no facets or collections are present
      const keywordWhere = await this.prepareCondition(keyword);
      andConditions.push(keywordWhere);
    }

    // Merge where conditions for main search
    if (Object.keys(where).length > 0) {
      andConditions.unshift(where);
    }
    if (andConditions.length > 0) {
      where = {and: andConditions};
    }

    const mergedIncludes = [
      ...(filter?.include ?? []),
      {relation: 'product', scope: {fields: {sellerId: true, status: true}}},
      {
        relation: 'productVariantPrice',
        scope: {fields: {price: true, mrp: true}},
      },
    ];

    // --- MAIN SEARCH ---
    const mainVariants = await this.productVariantProxy.find({
      ...filter,
      where,
      include: mergedIncludes,
    });

    const activeMainVariants =
      await this.productVariantService.filterOutInactiveSellerProductvariants(
        mainVariants as ProductVariantWithRelations[],
      );

    const enrichedMainResults = await Promise.all(
      activeMainVariants.map(item =>
        this.productVariantService.getProductVariantWithPresignedUrl(item),
      ),
    );

    let fuzzyResultsFull: ProductVariant[] = [];
    if (keyword) {
      const fuzzySuggestions = (await this.searchSuggestions(
        keyword,
      )) as ProductVariant[];
      if (fuzzySuggestions.length) {
        const fuzzyIds = fuzzySuggestions.map(s => s.id ?? '');

        // Filter out conditions that contain keyword-based searches
        const fuzzyConditions = andConditions.filter(cond => {
          // Remove conditions that have name-based searches or complex nested conditions
          if ('name' in cond || 'or' in cond) {
            return false;
          }
          // Also remove conditions that might contain nested keyword searches
          if ('and' in cond && Array.isArray(cond.and)) {
            return !cond.and.some(subCond => 'name' in subCond);
          }
          return true;
        });
        fuzzyConditions.push({id: {inq: fuzzyIds}});

        let fuzzyWhere: Where<ProductVariant> = {};
        if (Object.keys(where).length > 0) {
          fuzzyWhere = {and: fuzzyConditions};
        } else {
          fuzzyWhere = {id: {inq: fuzzyIds}};
        }

        const fullFuzzyVariants = await this.productVariantProxy.find({
          where: fuzzyWhere,
          include: mergedIncludes,
        });

        const activeFuzzyVariants =
          await this.productVariantService.filterOutInactiveSellerProductvariants(
            fullFuzzyVariants as ProductVariantWithRelations[],
          );

        fuzzyResultsFull = await Promise.all(
          activeFuzzyVariants.map(item =>
            this.productVariantService.getProductVariantWithPresignedUrl(item),
          ),
        );
      }
    }

    // --- MERGE RESULTS ---
    const seen = new Set<string>();
    const combinedResults = [
      ...enrichedMainResults,
      ...fuzzyResultsFull,
    ].filter(variant => {
      if (!variant.id) return false;
      if (seen.has(variant.id)) return false;
      seen.add(variant.id);
      return true;
    });

    return combinedResults;
  }

  private async buildFilters(
    variants: ProductVariantWithRelations[],
  ): Promise<FilterGroup[]> {
    const filters: FilterGroup[] = [];

    // All maps
    const collectionMap = new Map<
      string,
      {pvariantIds: string[]; collection: CollectionWithRelations}
    >();
    const discountMap = new Map<string, {name: string; variantIds: string[]}>();
    const customMap = new Map<string, {name: string; variantIds: string[]}>();
    const colorMap = new Map<string, {name: string; variantIds: string[]}>();
    const priceMap = new Map<
      string,
      {min: number; max: number; variantIds: string[]}
    >();
    const facetVariantMappings = new Map<string, string[]>();

    const variantIds: string[] = [];
    for (const variant of variants) {
      const variantId = variant.id!;
      variantIds.push(variantId);

      this.prepareFilterForFacet(variant, facetVariantMappings, variantId);
      this.prepareFilterForCollection(variant, collectionMap, variantId);
      this.prepareFilterForDiscount(variant, discountMap, variantId);
      this.prepareFilterForPrice(variant, priceMap, variantId);
      this.prepareCustomFilter(variant, customMap, variantId);
      this.prepareColorFilter(variant, colorMap, variantId);
    }

    // CATEGORY FILTER
    filters.push(
      new FilterGroup({
        label: 'Pick a Category',
        values: Array.from(collectionMap.entries()).map(
          ([colId, {pvariantIds, collection}]) => {
            const assetWithPreview = collection.featuredAsset
              ? this.getAssetWithPreview(collection.featuredAsset)
              : undefined;

            return new FilterValue({
              label: collection.name,
              value: colId,
              productVariantIds: pvariantIds,
              metadata: {
                previewUrl: assetWithPreview?.previewUrl ?? '',
                parentId: collection.parentId ?? null,
                position: collection.position ?? 0,
              },
            });
          },
        ),
      }),
    );

    // DISCOUNTS
    filters.push(
      new FilterGroup({
        label: 'Discounts',
        values: Array.from(discountMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    // PRICE RANGE
    let globalMin = Infinity;
    let globalMax = 0;
    for (const {min, max} of priceMap.values()) {
      if (min < globalMin) globalMin = min;
      if (max > globalMax) globalMax = max;
    }
    const roundTo = 100;
    globalMin = Math.floor(globalMin / roundTo) * roundTo;
    globalMax = Math.ceil(globalMax / roundTo) * roundTo;

    filters.push(
      new FilterGroup({
        label: 'Price',
        values: [
          new FilterValue({
            label: `${globalMin} - ${globalMax}`,
            value: `${globalMin}-${globalMax}`,
            productVariantIds: Array.from(priceMap.values()).flatMap(
              v => v.variantIds,
            ),
          }),
        ],
        metadata: {
          min: globalMin,
          max: globalMax,
          type: 'slider',
          facetId: 'price',
        },
      }),
    );

    // COLOURS
    filters.push(
      new FilterGroup({
        label: 'Colour',
        values: Array.from(colorMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    // CUSTOM
    filters.push(
      new FilterGroup({
        label: 'Custom',
        values: Array.from(customMap.entries()).map(([label, value]) => {
          return new FilterValue({
            label: value.name,
            value: label,
            productVariantIds: value.variantIds,
          });
        }),
      }),
    );

    const facetFilterGroup = await this.quickFilter(variantIds.join(','));
    if (facetFilterGroup?.values?.length) {
      filters.push(facetFilterGroup);
    }

    return filters;
  }

  private prepareFilterForCollection(
    variant: ProductVariantWithRelations,
    collectionMap: Map<
      string,
      {pvariantIds: string[]; collection: CollectionWithRelations}
    >,
    variantId: string,
  ) {
    const collection = variant.product?.collection as CollectionWithRelations;
    if (collection?.id) {
      const object = collectionMap.get(collection.id) ?? {
        pvariantIds: [],
        collection,
      };
      object.pvariantIds.push(variantId);
      collectionMap.set(collection.id, object);
    }
    return collectionMap;
  }

  private prepareFilterForDiscount(
    variant: ProductVariantWithRelations,
    discountMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantPrice) {
      const {mrp, price} = variant.productVariantPrice;
      const discount = ((mrp - price) / mrp) * 100;
      if (discount >= MINIMUM_DISCOUNT_THRESHOLD) {
        const discountLabel = getDiscountLabel(discount);
        if (discountLabel) {
          const object = discountMap.get(discountLabel) ?? {
            variantIds: [],
            name: discountLabel,
          };
          object.variantIds.push(variantId);
          discountMap.set(discountLabel, object);
        }
      }
    }
    return discountMap;
  }

  private prepareFilterForPrice(
    variant: ProductVariantWithRelations,
    priceMap: Map<string, {min: number; max: number; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantPrice) {
      const priceValue = variant.productVariantPrice.price ?? 0;

      const groupKey = '';

      const existing = priceMap.get(groupKey);
      if (existing) {
        existing.min = Math.min(existing.min, priceValue);
        existing.max = Math.max(existing.max, priceValue);
        existing.variantIds.push(variantId);
      } else {
        priceMap.set(groupKey, {
          min: priceValue,
          max: priceValue,
          variantIds: [variantId],
        });
      }
    }
    return priceMap;
  }

  private prepareCustomFilter(
    variant: ProductVariantWithRelations,
    customMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.product?.productCustomizationFields) {
      const object = customMap.get('Personalized') ?? {
        variantIds: [],
        name: 'Personalized',
      };
      object.variantIds.push(variantId);
      customMap.set('Personalized', object);
    }
    return customMap;
  }

  private prepareColorFilter(
    variant: ProductVariantWithRelations,
    colorMap: Map<string, {name: string; variantIds: string[]}>,
    variantId: string,
  ) {
    if (variant.productVariantOptions?.length) {
      for (const variantOption of variant.productVariantOptions) {
        // Check if the productOption and productOptionGroup are loaded
        if (variantOption.productOption?.productOptionGroup) {
          const optionGroup = variantOption.productOption.productOptionGroup;

          // Check if this option group is for colors
          if (optionGroup.unit === OptionUnit.COLOR) {
            const colorName = variantOption.productOption.name;
            const colorKey = colorName.toUpperCase();

            const object = colorMap.get(colorKey) ?? {
              name: colorName,
              variantIds: [],
            };

            object.variantIds.push(variantId);
            colorMap.set(colorKey, object);
          }
        }
      }
    }
    return colorMap;
  }
  private prepareFilterForFacet(
    variant: ProductVariantWithRelations,
    facetVariantMappings: Map<string, string[]>,
    variantId: string,
  ): void {
    if (variant.productVariantFacetValues?.length) {
      for (const pvFacet of variant.productVariantFacetValues) {
        const facetValueId = pvFacet.facetValueId;
        const variantIds = facetVariantMappings.get(facetValueId) ?? [];
        variantIds.push(variantId);
        facetVariantMappings.set(facetValueId, variantIds);
      }
    }
  }

  private async preparePriceFilter(priceRange: number[]): Promise<string[]> {
    const [min, max] = priceRange;
    const variants = await this.productVariantProxy.find({
      fields: {id: true},
      include: [
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {price: true},
            where: {price: {between: [min, max]}},
          },
          required: true,
        },
      ],
    } as AnyObject);
    return variants.map(v => v.id ?? '');
  }

  private async prepareThresholdFilter(
    type: 'price' | 'discount',
    threshold: number,
    operator: 'above' | 'below',
  ): Promise<string[]> {
    // Get all variants with pricing information
    const variants = await this.productVariantProxy.find({
      fields: {id: true},
      include: [
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {price: true, mrp: true},
          },
          required: true,
        },
      ],
    } as AnyObject);

    // Filter variants based on threshold criteria
    const filteredVariants = (variants as ProductVariantWithRelations[]).filter(
      (variant: ProductVariantWithRelations) => {
        if (!variant.productVariantPrice) {
          return false;
        }

        const {price, mrp} = variant.productVariantPrice;

        if (type === 'price') {
          // Filter by price threshold
          return operator === 'above' ? price > threshold : price < threshold;
        } else {
          // Filter by discount threshold
          if (mrp <= price || mrp === 0) {
            return false; // No discount
          }

          const discountPercentage = ((mrp - price) / mrp) * 100;
          return operator === 'above'
            ? discountPercentage > threshold
            : discountPercentage < threshold;
        }
      },
    );

    return filteredVariants.map(v => v.id ?? '');
  }

  getAssetWithPreview(asset: Asset): AssetResponseDto {
    const previewUrl = `${process.env.CDN_ORIGIN}/${asset.preview}`;
    return new AssetResponseDto({
      ...asset,
      previewUrl,
    });
  }

  async quickFilter(productVariantIds: string): Promise<FilterGroup> {
    const ids = productVariantIds.split(',').filter(Boolean);

    const productVariantFacetValues =
      await this.productVariantFacetValueProxy.find({
        where: {productVariantId: {inq: ids}},
        fields: {facetValueId: true},
      });

    const facetValueIds = Array.from(
      new Set(
        productVariantFacetValues
          .map(pvfv => pvfv.facetValueId)
          .filter((id): id is string => !!id),
      ),
    );

    if (!facetValueIds.length) {
      return new FilterGroup({
        label: 'Quick Filter',
        values: [],
        isFacet: true,
      });
    }

    const facetValues = await this.facetValueProxy.find({
      where: {id: {inq: facetValueIds}},
    });

    const facetIds = Array.from(
      new Set(facetValues.map(fv => fv.facetId).filter(Boolean)),
    );

    const facets = await this.facetProxy.find({
      where: {id: {inq: facetIds}},
    });

    const facetMap = new Map<string, string>();
    for (const facet of facets) {
      if (facet.id && facet.name) {
        facetMap.set(facet.id, facet.name);
      }
    }

    const values = facetValues.map(fv => ({
      label: fv.name,
      value: fv.id,
      metadata: {
        facetId: fv.facetId,
        facetName: facetMap.get(fv.facetId ?? '') ?? '',
      },
    }));

    return new FilterGroup({
      label: 'Quick Filter',
      values,
      isFacet: true,
    } as AnyObject);
  }
}
